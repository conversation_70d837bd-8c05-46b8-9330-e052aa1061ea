# 使用官方 Python 镜像作为基础镜像
FROM python:3.9-slim

# 设置工作目录
WORKDIR /app

# 安装必要的系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# 安装 Poetry 用于依赖管理
RUN pip install poetry

# 复制项目文件
COPY . .

# 安装项目依赖
RUN poetry config virtualenvs.create false \
    && poetry install --no-interaction --no-ansi

# 安装 Playwright 浏览器
RUN playwright install chromium

# 设置环境变量
ENV PYTHONUNBUFFERED=1

# 运行监控脚本
CMD ["python", "monitor.py"]
