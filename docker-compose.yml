version: '3.8'

services:
  bn_alpha_monitor:
    build: .
    container_name: bn_alpha_monitor
    restart: always
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
      - ./screenshots:/app/screenshots
    environment:
      - PYTHONUNBUFFERED=1
      - OLLAMA_BASE_URL=http://host.docker.internal:11434
    extra_hosts:
      - "host.docker.internal:host-gateway"
    networks:
      - bn_alpha_network

networks:
  bn_alpha_network:
    driver: bridge
