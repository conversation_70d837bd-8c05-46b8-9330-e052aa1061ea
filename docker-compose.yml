version: '3.8'

services:
  bn_alpha_monitor:
    build: .
    container_name: bn_alpha_monitor
    restart: always
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
      - ./screenshots:/app/screenshots
      - /tmp/.X11-unix:/tmp/.X11-unix:rw  # X11 socket for GUI apps
    environment:
      - PYTHONUNBUFFERED=1
      - DISPLAY=:99
      # Ollama 配置
      - OLLAMA_BASE_URL=http://host.docker.internal:11434
      - OLLAMA_MODEL=qwen2.5:3b
      # 飞书配置
      - FEISHU_WEBHOOK=${FEISHU_WEBHOOK}
      # 监控配置
      - MONITOR_INTERVAL=300
      - BROWSER_HEADLESS=true
      - BROWSER_TIMEOUT=30000
      # 日志配置
      - LOG_LEVEL=INFO
      - LOG_FILE=logs/monitor.log
      # 存储配置
      - SCREENSHOTS_DIR=screenshots
      - SENT_RECORDS_FILE=sent_records.json
    extra_hosts:
      - "host.docker.internal:host-gateway"
    networks:
      - bn_alpha_network
    # 健康检查
    healthcheck:
      test: ["CMD", "python", "/app/healthcheck.py"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    # 资源限制
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'

networks:
  bn_alpha_network:
    driver: bridge
